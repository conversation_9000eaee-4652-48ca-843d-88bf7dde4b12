<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>jo.capitalbank.library</groupId>
        <artifactId>common-parent-lib</artifactId>
        <version>1.0.0</version>
    </parent>

    <groupId>jo.capitalbank.microservices</groupId>

    <artifactId>microservice-template</artifactId>
    <version>1.0</version>
    <packaging>pom</packaging>
    <name>microservice-template</name>
    <description>Spring Boot Microservice Template for Capital Bank of Jordan</description>

    <modules>
        <module>api</module>
        <module>db</module>
    </modules>
    <properties>
        <java.version>21</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>jo.capitalbank.library</groupId>
            <artifactId>security-lib</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>jo.capitalbank.library</groupId>
            <artifactId>error-handling-lib</artifactId>
            <version>1.7</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.19.0</version>
        </dependency>
        <dependency>
            <groupId>jo.capitalbank.library</groupId>
            <artifactId>http-lib</artifactId>
            <version>2.9.2</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.5.5.Final</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.5.5.Final</version>
            <scope>provided</scope>
        </dependency>


    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>com.spotify</groupId>-->
<!--                <artifactId>dockerfile-maven-plugin</artifactId>-->
<!--                <version>1.4.13</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>default</id>-->
<!--                        <goals>-->
<!--                            <goal>build</goal>-->
<!--                            &lt;!&ndash; <goal>push</goal> &ndash;&gt;-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    <repository>loyalty-adapter</repository>-->
<!--                    <tag>0.0.2</tag>-->
<!--                    <skipDockerInfo>true</skipDockerInfo>-->

<!--                </configuration>-->
<!--            </plugin>-->
           <!-- <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>3.4.0</version>
                <configuration>
                    <from>
                        <image>eclipse-temurin:21-jre</image>
                    </from>
                    <to>
                        <image>muradshahin/loyalty-adapter:0.0.4</image>
                        <auth>
                            <username>muradshahin</username>
                            <password>************************************</password> &lt;!&ndash; use Docker Hub PAT &ndash;&gt;
                        </auth>
                    </to>
                </configuration>
            </plugin>-->

        </plugins>
    </build>
    <repositories>
<!--        <repository>-->
<!--            <id>nexus-releases</id>-->
<!--            <url>https://nexusnonprod.capitalbank.jo:8443/repository/maven-releases/</url>-->
<!--            <releases>-->
<!--                <enabled>true</enabled>-->
<!--            </releases>-->
<!--            <snapshots>-->
<!--                <enabled>true</enabled>-->
<!--            </snapshots>-->
<!--        </repository>-->

    <repository>
        <id>central</id>
        <url>https://repo.maven.apache.org/maven2</url>
        <releases>
            <enabled>true</enabled>
        </releases>
        <snapshots>
            <enabled>true</enabled>
        </snapshots>
    </repository>
</repositories>
</project>
