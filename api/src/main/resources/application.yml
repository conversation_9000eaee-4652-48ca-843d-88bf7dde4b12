# Server Configuration
server:
  port: 8080
  servlet:
    context-path: /api


spring:
  profiles:
    active: ${PROFILE}

  application:
    name: microservice-template
  # Database Configuration
  jpa:
    database-platform: org.hibernate.dialect.SQLServerDialect
    show-sql: true
    #hibernate:
      #ddl-auto: validate
    properties:
      hibernate:
        format_sql: true

  # Flyway Configuration
  flyway:
    enabled: true
    baseline-on-migrate: true
    locations: classpath:db/migration
    validate-on-migrate: true
  # Jackson Configuration
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: non_null
    time-zone: UTC
#  main: [<PERSON>] we need to make sure if we want to enable it. maybe will be needed with libraries approach
#    allow-bean-definition-overriding: true
  web:
    resources:
      add-mappings: false

# Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
  info:
    env:
      enabled: true

# OpenAPI Configuration
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method

# Logging Configuration
logging:
  level:
    root: ${LOGGING}
  structured.format.console: logstash
#  pattern:
#    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n'
