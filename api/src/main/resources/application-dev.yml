# Server Configuration
server:
  port: 8083

# CBOJ Custom Properties
# OAuth2 Security Configuration
cboj:
  security:
    oauth2:
      enabled: ${oauth2.enabled}

spring:
  # Database Configuration
  #datasource:
    #driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    #url: *********************************************************************************************
    #username: sa
    #password: mY_9pAssw0rd1

  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://sso-stg-cboj.capitalbank.jo:8443/auth/realms/cboj-mai




http-lib:
  tls:
    keystore:
      path: ""
      password: ""

