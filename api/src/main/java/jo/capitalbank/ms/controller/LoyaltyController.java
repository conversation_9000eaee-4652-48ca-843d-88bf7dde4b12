package jo.capitalbank.ms.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import jo.capitalbank.ms.models.requests.CalculationRequest;
import jo.capitalbank.ms.models.requests.LoyaltyGainRequest;
import jo.capitalbank.ms.models.requests.RedeemRequest;
import jo.capitalbank.ms.services.CustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@Tag(name = "Loyalty API end-points")
@RequestMapping("/v1/")
public class LoyaltyController {


    @Autowired
    CustomerService customerService;

    @GetMapping("customers/{id}")
    public ResponseEntity<?> getCustomerDetails(@PathVariable long id, @RequestHeader HttpHeaders channelHeaders) {

        log.info("get customer details ---> id={}", id);
        return null;
    }


    @PostMapping("customers/gain")
    public ResponseEntity<?> gainLoyalty(@RequestHeader HttpHeaders channelHeaders, @RequestBody LoyaltyGainRequest request) {
        log.info("gain loyalty --> id={}", request.getIdentifierValue());
        return null;
    }

    @PostMapping("customers/redeem")
    public ResponseEntity<?> redeem(@RequestHeader HttpHeaders channelHeaders, @RequestBody RedeemRequest request) {
        log.info("redeem --> id={}", request.getIdentifierValue());
        return null;
    }

    @PostMapping("customers/amount-calculation")
    public ResponseEntity<?> calculateAmount(@RequestHeader HttpHeaders channelHeaders, @RequestBody CalculationRequest request) {
        log.info("calculateAmount ---> id={}", request.getIdentifierValue());
        return null;
    }

    @GetMapping("customers/steps-info/{id}")
    public ResponseEntity<?> stepInfo(@RequestHeader HttpHeaders channelHeaders, @PathVariable long id) {
        log.info("stepInfo --> id={}", id);
        return null;
    }

    @GetMapping("customers/transactions/{id}")
    public ResponseEntity<?> customerTransactions(@RequestHeader HttpHeaders channelHeaders, @PathVariable long id,
                                                  @RequestParam(required = false) String pageSize,
                                                  @RequestParam(required = false) String pageNumber,
                                                  @RequestParam(required = false) String sort,
                                                  @RequestParam(required = false) String type) {
        log.info("customerTransactions ---> id={}", id);
        return null;
    }
}
