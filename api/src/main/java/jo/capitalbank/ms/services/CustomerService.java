package jo.capitalbank.ms.services;


import jo.capitalbank.ms.dto.CustomerDto;
import jo.capitalbank.ms.library.common.services.HttpClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class CustomerService {

//    private final CustomerRepository customerRepository;

    @Autowired
    private HttpClientService httpClientService;

    public CustomerDto getCustomerById(long id) {
        return CustomerDto.builder()
                .firstName("first name ")
                .build();
        //throw new OBBadRequestException("Customer Not Found");


    }


    public List<CustomerDto> getAllCustomers() {
        return new ArrayList<>();
    }

    public CustomerDto sendCreationRequest(CustomerDto customer) {

        ResponseEntity<?> responseEntity = httpClientService.sendRequest("api.restful-api.dev/objects/7", HttpMethod.GET, HttpHeaders.EMPTY, null, null);

        return CustomerDto.builder()
                .firstName("first name ")
                .build();
    }
}
