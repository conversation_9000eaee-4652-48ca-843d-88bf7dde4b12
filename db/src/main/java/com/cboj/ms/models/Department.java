package com.cboj.ms.models;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "test_departments")
public class Department {

    @Id
    private Integer id;

    @Column(name = "dep_name")
    private String depName;

    @Column(name = "location")
    private String location;

    @Column(name = "manager_name")
    private String managerName;
}
